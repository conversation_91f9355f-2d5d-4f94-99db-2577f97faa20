-- WITH template AS (
--   INSERT INTO templates_products_lists (name, external_id)
--   VALUES ('Compras do mês', '01JR0YDFS87ZNSH4DSCER8DJBA')
--   RETURNING id
-- )
-- INSERT INTO templates_products_lists_items (template_id, product_id, quantity)
-- SELECT
--   t.id,
--   p.product_id,
--   p.quantity
-- FROM template t,
--   (VALUES
--     (2722, 2),
--     (2734, 1),
--     (2719, 1),
--     (2737, 1),
--     (2733, 1),
--     (2736, 1)
--   ) AS p(product_id, quantity);